import type { LogR<PERSON>ord, Theme } from "./types";

const code = (n: number) => (s: string) => `\u001b[${n}m${s}\u001b[0m`;
const themeDefaults: Theme = {
	dim: code(2),
	gray: code(90),
	red: code(31),
	yellow: code(33),
	green: code(32),
	cyan: code(36),
	magenta: code(35),
	bold: code(1),
	reset: (s) => s,
};
const levelColor = (lvl: LogRecord["level"], t: Theme = themeDefaults) =>
	({
		trace: t.gray,
		debug: t.cyan,
		info: t.green,
		warn: t.yellow,
		error: t.red,
		fatal: t.red,
	})[lvl];
export function jsonLine(rec: LogRecord) {
	return JSON.stringify(rec);
}
function p2(n: number) {
	return n < 10 ? `0${n}` : String(n);
}
function fmt(d: Date) {
	return `${d.getFullYear()}-${p2(d.getMonth() + 1)}-${p2(d.getDate())} ${p2(d.getHours())}:${p2(d.getMinutes())}:${p2(d.getSeconds())}.${String(d.getMilliseconds()).padStart(3, "0")}`;
}
function render(obj: any, acc: string[] = [], prefix = "") {
	if (!obj || typeof obj !== "object") return acc;
	const ent = Object.entries(obj);
	ent.forEach(([k, v], i) => {
		const last = i === ent.length - 1;
		const br = last ? "└─" : "├─";
		const next = prefix + (last ? "  " : "│ ");
		if (v && typeof v === "object") {
			acc.push(`${prefix}${br} ${k}:`);
			render(v, acc, next);
		} else {
			acc.push(`${prefix}${br} ${k}: ${String(v)}`);
		}
	});
	return acc;
}
export function prettyLine(rec: LogRecord, theme?: Partial<Theme>) {
	const t = { ...themeDefaults, ...(theme || {}) } as Theme;
	const ts = fmt(new Date(rec.time));
	const c = levelColor(rec.level, t)!;
	const head = `${t.dim("[")}${t.bold(ts)}${t.dim("]")} ${c(rec.level.toUpperCase())}`;
	const src = [rec.service, rec.env].filter(Boolean).join(" • ");
	const srcStr = src ? ` ${t.dim("(")}${src}${t.dim(")")}` : "";
	const lines = [`${head}${srcStr} ${t.bold(rec.msg)}`];
	const meta: any = {};
	if (rec.traceId) meta.traceId = rec.traceId;
	if (rec.spanId) meta.spanId = rec.spanId;
	if (rec.version) meta.version = rec.version;
	const tree = render({ ...meta, ...(rec.context || {}) });
	if (tree.length) lines.push(...tree);
	if (rec.err) {
		const e: any = rec.err;
		lines.push(`└─ error: ${t.red(e.name || "Error")}: ${e.message || ""}`);
		if (e.stack)
			lines.push(
				...String(e.stack)
					.split("\n")
					.map((l) => t.gray("   " + l)),
			);
	}
	return lines.join("\n");
}
export function ecsObject(rec: LogRecord) {
	const base: any = {
		"@timestamp": new Date(rec.time).toISOString(),
		message: rec.msg,
		"log.level": rec.level,
		"service.name": rec.service,
		"event.dataset": rec.env
			? `${rec.service || "app"}.${rec.env}`
			: rec.service || "app",
		"trace.id": rec.traceId,
		"span.id": rec.spanId,
		labels: rec.context,
	};
	if (rec.err) {
		base["error.type"] = (rec.err as any).name;
		base["error.message"] = (rec.err as any).message;
		if ((rec.err as any).stack)
			base["error.stack_trace"] = (rec.err as any).stack;
	}
	if (rec.version) base["service.version"] = rec.version;
	return base;
}
export const ecsLine = (rec: LogRecord) => JSON.stringify(ecsObject(rec));
export const datadogObject = (rec: LogRecord) => ({
	message: rec.msg,
	status: rec.level,
	level: rec.level,
	service: rec.service,
	env: rec.env,
	timestamp: rec.time,
	trace_id: rec.traceId,
	span_id: rec.spanId,
	error: rec.err || undefined,
	attributes: rec.context,
	logger: { name: "@acme/node-logger-lib", version: rec.version },
	ddsource: "node",
});
export const datadogLine = (rec: LogRecord) =>
	JSON.stringify(datadogObject(rec));
export const splunkObject = (rec: LogRecord) => ({
	time: Math.floor(rec.time / 1000),
	sourcetype: "_json",
	source: rec.service || "app",
	event: {
		message: rec.msg,
		level: rec.level,
		service: rec.service,
		env: rec.env,
		version: rec.version,
		traceId: rec.traceId,
		spanId: rec.spanId,
		context: rec.context,
		error: rec.err || undefined,
	},
});
export const splunkLine = (rec: LogRecord) => JSON.stringify(splunkObject(rec));
