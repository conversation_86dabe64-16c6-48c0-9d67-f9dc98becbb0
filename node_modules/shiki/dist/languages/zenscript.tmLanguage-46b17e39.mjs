var zenscript_tmLanguage = {fileTypes:["zs"],name:"zenscript",patterns:[{comment:"numbers",name:"constant.numeric.zenscript",match:"\\b((0(x|X)[0-9a-fA-F]*)|(([0-9]+\\.?[0-9]*)|(\\.[0-9]+))((e|E)(\\+|-)?[0-9]+)?)([LlFfUuDd]|UL|ul)?\\b"},{comment:"prefixedNumbers",name:"constant.numeric.zenscript",match:"\\b\\-?(0b|0x|0o|0B|0X|0O)(0|[1-9a-fA-F][0-9a-fA-F_]*)[a-zA-Z_]*\\b"},{include:"#code"},{comment:"arrays",name:"storage.type.object.array.zenscript",match:"\\b((?:[a-z]\\w*\\.)*[A-Z]+\\w*)(?=\\[)"}],repository:{code:{patterns:[{include:"#class"},{include:"#functions"},{include:"#dots"},{include:"#quotes"},{include:"#brackets"},{include:"#comments"},{include:"#var"},{include:"#keywords"},{include:"#constants"},{include:"#operators"}]},"class":{comment:"class",name:"meta.class.zenscript",match:"(zenClass)\\s+(\\w+)",captures:{"1":{name:"storage.type.zenscript"},"2":{name:"entity.name.type.class.zenscript"}}},functions:{comment:"functions",name:"meta.function.zenscript",match:"function\\s+([A-Za-z_$][\\w$]*)\\s*(?=\\()",captures:{"0":{name:"storage.type.function.zenscript"},"1":{name:"entity.name.function.zenscript"}}},dots:{comment:"dots",name:"plain.text.zenscript",match:"\\b(\\w+)(\\.)(\\w+)((\\.)(\\w+))*",captures:{"1":{name:"storage.type.zenscript"},"2":{name:"keyword.control.zenscript"},"5":{name:"keyword.control.zenscript"}}},quotes:{patterns:[{begin:"\"",beginCaptures:{"0":{name:"punctuation.definition.string.begin.zenscript"}},end:"\"",endCaptures:{"0":{name:"punctuation.definition.string.end.zenscript"}},name:"string.quoted.double.zenscript",patterns:[{match:"\\\\.",name:"constant.character.escape.zenscript"}]},{begin:"'",beginCaptures:{"0":{name:"punctuation.definition.string.begin.zenscript"}},end:"'",endCaptures:{"0":{name:"punctuation.definition.string.end.zenscript"}},name:"string.quoted.single.zenscript",patterns:[{match:"\\\\.",name:"constant.character.escape.zenscript"}]}]},brackets:{patterns:[{comment:"items and blocks",name:"keyword.other.zenscript",match:"(<)\\b(.*?)(:(.*?(:(\\*|\\d+)?)?)?)(>)",captures:{"1":{name:"keyword.control.zenscript"},"2":{name:"keyword.other.zenscript"},"3":{name:"keyword.control.zenscript"},"4":{name:"variable.other.zenscript"},"5":{name:"keyword.control.zenscript"},"6":{name:"constant.numeric.zenscript"},"7":{name:"keyword.control.zenscript"}}}]},comments:{patterns:[{comment:"inline comments",name:"comment.line.double=slash",match:"//[^\n]*"},{comment:"block comments",name:"comment.block",begin:"\\/\\*",beginCaptures:{"0":{name:"comment.block"}},end:"\\*\\/",endCaptures:{"0":{name:"comment.block"}}}]},"var":{comment:"var",match:"\\b(val|var)\\b",name:"storage.type"},keywords:{patterns:[{comment:"statement keywords",name:"keyword.control.zenscript",match:"\\b(instanceof|get|implements|set|import|function|override|const|if|else|do|while|for|throw|panic|lock|try|catch|finally|return|break|continue|switch|case|default|in|is|as|match|throws|super|new)\\b"},{comment:"storage keywords",name:"storage.type.zenscript",match:"\\b(zenClass|zenConstructor|alias|class|interface|enum|struct|expand|variant|set|void|bool|byte|sbyte|short|ushort|int|uint|long|ulong|usize|float|double|char|string)\\b"},{comment:"modifier keywords",name:"storage.modifier.zenscript",match:"\\b(variant|abstract|final|private|public|export|internal|static|protected|implicit|virtual|extern|immutable)\\b"},{comment:"annotation keywords",name:"entity.other.attribute-name",match:"\\b(Native|Precondition)\\b"},{comment:"language keywords",name:"constant.language",match:"\\b(null|true|false)\\b"}]},operators:{patterns:[{comment:"math operators",name:"keyword.control",match:"\\b(\\.|\\.\\.|\\.\\.\\.|,|\\+|\\+=|\\+\\+|-|-=|--|~|~=|\\*|\\*=|/|/=|%|%=|\\||\\|=|\\|\\||&|&=|&&|\\^|\\^=|\\?|\\?\\.|\\?\\?|<|<=|<<|<<=|>|>=|>>|>>=|>>>|>>>=|=>|=|==|===|!|!=|!==|\\$|`)\\b"},{comment:"colons",name:"keyword.control",match:"\\b(;|:)\\b"}]}},scopeName:"source.zenscript"};

export { zenscript_tmLanguage as default };
