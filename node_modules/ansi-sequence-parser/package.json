{"name": "ansi-sequence-parser", "description": "A parser for ANSI escape sequences", "repository": {"type": "git", "url": "https://github.com/blake-mealey/ansi-sequence-parser.git"}, "homepage": "https://github.com/blake-mealey/ansi-sequence-parser#readme", "version": "1.1.3", "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "files": ["dist", "CHANGELOG.md", "README.md"], "keywords": ["ansi", "sequences", "parser"], "author": "blake-mealey", "license": "MIT", "devDependencies": {"@changesets/cli": "^2.26.0", "chalk": "^5.4.1", "tsup": "^6.5.0", "typescript": "^4.9.4", "vitest": "^0.28.3"}, "packageManager": "pnpm@8.15.1", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm build", "test": "FORCE_COLOR=true vitest", "changeset": "changeset"}}