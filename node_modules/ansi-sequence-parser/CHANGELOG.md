# ansi-sequence-parser

## 1.1.3

### Patch Changes

- e4b8472: - Fixes escape code 22 not resetting `bold` decorations
  - Adds support for `overline` and `hidden` decorations

## 1.1.2

### Patch Changes

- f88617a: include intended fix from 1.1.1 (the fix was not actually included in the previous release)

## 1.1.1

### Patch Changes

- b5ccaf4: fix: don't infinite loop when parsing an unclosed sequence

## 1.1.0

### Minor Changes

- 57cbaa6: Add default named colors map

## 1.0.0

### Major Changes

- 86719a2: Initial release
