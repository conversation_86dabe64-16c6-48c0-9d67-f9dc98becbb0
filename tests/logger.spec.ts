import { describe, expect, it } from "vitest";
import { createLogger } from "../src/logger.js";

describe("logger", () => {
	it("logs without throwing", () => {
		const log = createLogger({ pretty: { enabled: false } });
		log.info("hello");
		log.error("oops", new Error("boom"));
		expect(true).toBe(true);
	});

	it("logs to console", () => {
		const log = createLogger({ pretty: { enabled: true } });
		log.info("hello");
		log.error("oops", new Error("boom"));
		expect(true).toBe(true);
	});
});
